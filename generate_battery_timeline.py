#!/usr/bin/env python3
"""

Data Sources:
- working_vehicles.csv (matching vehicles)
- working_unique_vehicles.csv (unique vehicles)
- hv_repair_2025-06-02b.csv (repair events)
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
import logging
import sys
from typing import Dict, List, Tuple, Optional, Set, Any, TypedDict
from collections import defaultdict
from sqlalchemy import create_engine, text
import warnings

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("generate_battery_timeline.log"),
    ],
)
logger = logging.getLogger(__name__)


class BatteryEvent(TypedDict):
    """
    Structure of a battery event as processed from repair data.

    Fields:
    - vin: Vehicle identification number where event occurred
    - date: Date when the event happened (datetime.date object)
    - column: Event type - "old" (removal), "new" (installation), or None (working-only vehicle)
    - event_id: Unique identifier for the repair record (DataFrame index or None for working-only)
    - row_data: Complete repair record data (pandas Series or None for working-only)
    """

    vin: str
    date: datetime.date  # Note: this is datetime.date, not datetime.datetime
    column: Optional[str]  # "old", "new", or None
    event_id: Optional[int]  # DataFrame index or None
    row_data: Optional[Any]  # pandas Series or None


class BatteryInterval(TypedDict):
    """
    Structure of a battery lifecycle interval (one residence period in a vehicle).

    Fields:
    - battery_id: Unique battery identifier
    - vin: Vehicle where battery resided during this interval
    - interval_start: Installation date (datetime.date)
    - interval_end: Removal date (datetime.date or None if still installed)
    - interval_type: Classification - "original", "replacement", "swap", "unknown"
    - source_event_ids: List of repair event IDs that created/closed this interval
    - confidence: Data quality score (0.0-1.0)
    - notes: Additional context or error information
    """

    battery_id: str
    vin: str
    interval_start: Optional[datetime.date]  # None for unknown start
    interval_end: Optional[datetime.date]  # None for currently installed
    interval_type: (
        str  # "based  from start and end, it can be record_start_infer_end,...
    )
    lifecycle_stage: str  # initial, Current, Orphaned, Ghost, Intermediate (Battery installed and later removed)
    source_event_ids: List[Optional[int]]  # Event IDs that created this interval
    confidence: float  # 0.0-1.0 data quality score
    notes: str  # Additional context


class BatteryProcessor:
    """
    Processes lifecycle events for a single battery using state machine approach.

    This class tracks the complete lifecycle of one battery through multiple vehicles.
    It processes chronologically ordered events (installations/removals) and maintains:
    - One open_interval at most (current installation)
    - Multiple completed_intervals (past residence periods)

    State transitions:
    - "new" event: Installation → opens new interval (closes previous if exists)
    - "old" event: Removal → closes current interval
    - None event: Working-only vehicle → creates interval from erstzulassung
    """

    def __init__(self, battery_id: str):
        self.today = datetime.now().date()
        self.battery_id = battery_id
        self.open_interval = None  # Current active interval (if any)
        self.completed_intervals: List[BatteryInterval] = []  # List of closed intervals
        self.appearance_counter = 0  # Track appearance order for classification

    def process_event(self, event: BatteryEvent) -> None:
        """
        Process a single battery event (installation or removal).

        Args:
            event: BatteryEvent containing vin, date, column, event_id, and row_data
        """
        self.appearance_counter += 1

        if event["column"] == "new":  # Installation event
            self._handle_installation(event)
        elif event["column"] == "old":  # Removal event
            self._handle_removal(event)
        elif event["column"] is None:  # Working-only vehicle
            self._handle_working_only(event)

    def _handle_installation(self, event: BatteryEvent) -> None:
        """
        Args:
            event: Installation event with column="new"
        """
        if self.open_interval:
            # Close previous interval (assume removal before installation)
            self._close_interval(
                event["date"],
                confidence=0.7,
                note="Missing removal - Auto-closed before new installation",
                interval_type="record_start_infer_end",
            )

        self.open_interval = {
            "vin": event["vin"],
            "start": event["date"],
            "open_src": event.get("event_id"),
            "interval_type": "record_start_ongoing",
            "confidence": 0.85,
            "battery_id": self.battery_id,
            "notes": "",
        }

    def _handle_removal(self, event: BatteryEvent) -> None:
        """
        Args:
            event: Removal event with column="old"
        """
        if self.open_interval and self.open_interval["vin"] == event["vin"]:
            self._close_interval(
                event["date"],
                confidence=0.95,
                note="",
                interval_type="record_start_record_end",
            )
        elif self.open_interval:
            # Removal from different vehicle - possible data error
            self._close_interval(
                event["date"],
                confidence=0.6,
                note=f"Removal event from {event['vin']} but was installed in {self.open_interval['vin']} on {self.open_interval['start']}",
                interval_type="record_start_record_end_mismatch",
            )
        else:
            self._handle_orphaned_removal(event)

    def _handle_working_only(self, event: BatteryEvent) -> None:
        """
        Handle battery from working-only vehicle (no repair history).

        Args:
            event: Working-only event with column=None
        """
        if not self.open_interval:
            # Create open interval starting from erstzulassung
            self.open_interval = {
                "vin": event["vin"],
                "start": event["date"],
                "open_src": None,
                "battery_id": self.battery_id,
                "notes": "Working-only vehicle - Start date from erstzulassung",
                "confidence": 0.6,
                "interval_type": "estimate_start_ongoing",
            }

    def _handle_orphaned_removal(self, event: BatteryEvent) -> None:
        """
        Handle removal event without corresponding installation.
        """
        is_first_appearance = self.appearance_counter == 1

        # first time seeing this battery in "old" column, without prior installation, high chance that this battery is first installed into this vehicle
        if is_first_appearance:
            interval = {
                "battery_id": self.battery_id,
                "vin": event["vin"],
                "interval_start": None,
                "interval_end": event["date"],
                "interval_type": "infer_start_record_end",
                "source_event_ids": [event.get("event_id")],
                "confidence": 0.8,
                "notes": "First appearance - high confidence for erstzulassung start",
                "erstzulassung_candidate": True,
            }
        else:
            interval = {
                "battery_id": self.battery_id,
                "vin": event["vin"],
                "interval_start": None,
                "interval_end": event["date"],
                "interval_type": "orphaned_removal",
                "source_event_ids": [event.get("event_id")],
                "confidence": 0.5,
                "interval_type": "orphaned_removal",
                "notes": "Removal without installation record",
                "erstzulassung_candidate": False,
            }

        self.completed_intervals.append(interval)

    def _close_interval(
        self,
        end_date: datetime,
        confidence: float = 0.95,
        note: str = "",
        interval_type: str = "",
    ) -> None:
        """Close the currently opened-interval."""
        if not self.open_interval:
            return

        interval = {
            "battery_id": self.battery_id,
            "vin": self.open_interval["vin"],
            "interval_start": self.open_interval["start"],
            "interval_end": end_date,
            "interval_type": interval_type or self.open_interval["interval_type"],
            "source_event_ids": [self.open_interval["open_src"]],
            "confidence": confidence,
            "notes": note,
        }
        self.completed_intervals.append(interval)
        self.open_interval = None

    def finalize(self) -> List[BatteryInterval]:
        """
        Finalize processing and return all intervals (including open ones).

        Returns:
            List[BatteryInterval]: Complete list of battery residence intervals
        """
        intervals = self.completed_intervals.copy()

        # Add open interval as ongoing
        if self.open_interval:
            interval: BatteryInterval = {
                "battery_id": self.battery_id,
                "vin": self.open_interval["vin"],
                "interval_start": self.open_interval["start"],
                "interval_end": self.today,  # fill today to differentiate interval that has missing end
                "interval_type": self.open_interval["interval_type"],
                "source_event_ids": [self.open_interval["open_src"]],
                "notes": self.open_interval["notes"],
                "confidence": self.open_interval["confidence"],
            }
            intervals.append(interval)

        return intervals


class BatteryTimelineGenerator:
    """Calculate battery ages based on repair events and vehicle data."""

    def __init__(self):
        self.today = datetime.now().date()

        self.db_engine = None

        # Data containers
        self.hv_repair_df = None
        self.daily_stats_df = None
        self.working_vehicles_df = None
        self.working_unique_df = None

        # Processing results
        self.daily_stats_by_vehicle = {}  # Pre-indexed by vehicle_id for fast lookup
        self.battery_vehicles = {}  # battery_id -> list of battery appearance dicts
        self.vehicle_info = {}  # vin -> vehicle info
        self.vin_to_vehicle_id = {}
        self.unique_vehicles = set()
        self.unique_batteries = set()
        self.vin_without_vehicle_id = set()

        # Enhanced lifecycle tracking
        self.battery_processors = {}  # battery_id -> BatteryProcessor
        self.battery_timelines = []  # Complete interval records
        self.raw_battery_timelines = []  # Complete interval records before vin stiching

        # ─── conflict bookkeeping ──────────────────────────────
        self.conflicts = []
        self.phantom_battery_timelines = []

        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def _initialize_database_connection(self):
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")
        db_connection_string = (
            f"postgresql://{user}:{password}@{host}:{port}/{database}"
        )
        try:
            self.db_engine = create_engine(db_connection_string)
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            logger.error(
                "Pipeline requires PostgreSQL connection for activity validation"
            )
            logger.error(f"Connection string: {db_connection_string}")
            logger.error("Please ensure PostgreSQL server is running and accessible")
            raise ConnectionError(f"Required database connection failed: {e}")

    def _load_vin_mappings(self):
        """Load VIN to vehicle_id mapping from database"""
        if not self.db_engine:
            logger.warning(
                "No database connection - cant not load VIN to vehicle_id mapping"
            )
            raise

        try:
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles 
            WHERE vin IS NOT NULL
            """

            mapping_df = pd.read_sql(mapping_query, self.db_engine)
            logger.info(
                f"Loaded VIN mapping for {len(mapping_df):,} vehicles from database"
            )

            # Build VIN to vehicle_id mapping
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Built VIN to vehicle_id mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )

        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    def load_data(self):
        """Load all data files."""
        logger.info("Loading data files...")

        self._initialize_database_connection()

        # Load HV repair data
        logger.info("Loading HV repair data...")
        self.hv_repair_df = pd.read_csv("hv_repair_2025-06-02b.csv")
        logger.info(f"Loaded {len(self.hv_repair_df)} HV repair records")

        # Load working vehicles data
        logger.info("Loading working vehicles data and daily stats...")
        self.working_vehicles_df = pd.read_csv(
            "comparison_results/working_matching_vehicles.csv"
        )
        self.working_unique_df = pd.read_csv(
            "comparison_results/working_unique_vehicles.csv"
        )
        self.daily_stats_df = pd.read_csv(
            "daily_stats.csv",
            dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
            parse_dates=["date"],
        )

        logger.info(f"Loaded {len(self.working_vehicles_df)} matching vehicles")
        logger.info(f"Loaded {len(self.working_unique_df)} unique vehicles")
        logger.info(f"Loaded {len(self.daily_stats_df)} daily stats records")

        # Combine working vehicles
        self.working_vehicles_df = pd.concat(
            [self.working_vehicles_df, self.working_unique_df], ignore_index=True
        )
        logger.info(f"Total working vehicles: {len(self.working_vehicles_df)}")

        logger.info("Loading VIN to vehicle_id mapping from database...")
        self._load_vin_mappings()

        logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")
        for vehicle_id, group in self.daily_stats_df.groupby("vehicle_id"):
            # Sort by date for each vehicle
            vehicle_data = group.sort_values("date").copy()
            self.daily_stats_by_vehicle[vehicle_id] = vehicle_data
        logger.info(
            f"Pre-indexed daily stats for {len(self.daily_stats_by_vehicle):,} vehicles"
        )
        logger.info(
            "Memory optimization: clear dailystats dataframe as we now have indexed data"
        )
        self.daily_stats_df = None

    def clean_data(self):
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        # Clean HV repair data
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )

        # Create effective date (battery_changed if available, otherwise created)
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])

        # Clean battery IDs
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                ["nan", "", " ", "None"], None
            )

        # Clean working vehicles data
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )

        for col in ["master", "slave"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(
                    str
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        # Filter valid records
        self.hv_repair_df = self.hv_repair_df.dropna(subset=["vin", "effective_date"])
        self.working_vehicles_df = self.working_vehicles_df.dropna(subset=["vin"])

        logger.info(
            f"After cleaning: {len(self.hv_repair_df)} repair records, {len(self.working_vehicles_df)} working vehicles"
        )

    def process_hv_repair_data(self):
        """Process HV repair data to track battery appearances."""
        logger.info("Processing HV repair data...")

        # Sort by effective date to process chronologically. First event = first appearance = earliest seen date of batteries (old or new)
        self.hv_repair_df = self.hv_repair_df.sort_values("effective_date").reset_index(
            drop=True
        )

        for idx, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            effective_date = row["effective_date"].date()
            old_battery = row["battery_id_old"]
            new_battery = row["battery_id_new"]
            event_id = idx  # Use DataFrame index as event ID
            # Process old battery appearance
            if old_battery and pd.notna(old_battery):
                self.unique_batteries.add(old_battery)
                if old_battery not in self.battery_vehicles:
                    self.battery_vehicles[old_battery] = []
                self.battery_vehicles[old_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "old",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {old_battery} appeared as old in vehicle {vin} on {effective_date}"
                )

            # Process new battery appearance
            if new_battery and pd.notna(new_battery):
                self.unique_batteries.add(new_battery)
                if new_battery not in self.battery_vehicles:
                    self.battery_vehicles[new_battery] = []
                self.battery_vehicles[new_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "new",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {new_battery} appeared as new in vehicle {vin} on {effective_date}"
                )

            self.unique_vehicles.add(vin)

        logger.info(f"Found {len(self.unique_batteries)} batteries from repair data")

    def _get_first_active_date_for_vin(self, vin: str) -> datetime:
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get first active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                first_date = vehicle_data["date"].min()
                if pd.notna(first_date):
                    return first_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting first active date for {vin}: {e}")
            return None

    def _get_last_active_date_for_vin(self, vin: str) -> datetime:
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get last active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                last_date = vehicle_data["date"].max()
                if pd.notna(last_date):
                    return last_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting last active date for {vin}: {e}")
            return None

    def build_vehicle_info(self):
        logger.info("Building vehicle information...")

        for _, row in self.working_vehicles_df.iterrows():
            vin = row["vin"]
            self.unique_vehicles.add(vin)
            first_active_date = self._get_first_active_date_for_vin(vin)
            self.vehicle_info[vin] = {
                "vin": vin,
                "erstzulassung": (
                    row.get("erstzulassung").date()
                    if pd.notna(row.get("erstzulassung"))
                    else first_active_date
                ),
                "first_active_date": first_active_date,
                "master_battery": row.get("master"),
                "slave_battery": row.get("slave"),
                "akz": row.get("akz"),
                "first_active_date": first_active_date,
                "last_active_date": self._get_last_active_date_for_vin(vin),
            }
        # We also need to get vehicle info for vehicles that appear in repair data but not in working data
        for _, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            if vin not in self.vehicle_info:
                first_active_date = self._get_first_active_date_for_vin(vin)
                self.vehicle_info[vin] = {
                    "vin": vin,
                    "erstzulassung": first_active_date,
                    "first_active_date": first_active_date,
                    "master_battery": None,
                    "slave_battery": None,
                    "akz": None,
                    "last_active_date": self._get_last_active_date_for_vin(vin),
                }

        logger.info(f"Built info for {len(self.vehicle_info)} vehicles")

        # Count number of vehicles that have both master and slave batteries
        vehicles_with_both_batteries = 0
        for vehicle_info in self.vehicle_info.values():
            if vehicle_info["master_battery"] and vehicle_info["slave_battery"]:
                vehicles_with_both_batteries += 1
        logger.info(
            f"Dual-battery vehicles from working data: {vehicles_with_both_batteries}"
        )

    def add_vehicles_from_working_only_data(self):
        logger.info("Adding batteries from working-only vehicles...")

        repair_vins = set(self.hv_repair_df["vin"].unique())
        working_vins = set(self.vehicle_info.keys())

        # Add batteries from vehicles that only appear in working data
        working_only_vins = working_vins - repair_vins
        for vin in working_only_vins:
            self._add_working_only_batteries(vin)

        self.stats["working_only_vehicles"] = len(working_only_vins)
        logger.info(
            f"Added batteries from {len(working_only_vins)} working-only vehicles"
        )

    def _add_working_only_batteries(self, vin: str):
        """Add batteries from vehicles that only appear in working data."""
        vehicle_info = self.vehicle_info[vin]
        erstzulassung = vehicle_info["erstzulassung"]

        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return

        # Add batteries from working data
        for battery_field in ["master_battery", "slave_battery"]:
            battery_id = vehicle_info[battery_field]
            if battery_id and pd.notna(battery_id):
                self.unique_batteries.add(battery_id)
                if battery_id not in self.battery_vehicles:
                    self.battery_vehicles[battery_id] = []

                # Check for duplicates - don't add if battery already has events for this VIN
                if not any(e["vin"] == vin for e in self.battery_vehicles[battery_id]):
                    self.battery_vehicles[battery_id].append(
                        {
                            "vin": vin,
                            "date": erstzulassung,
                            "column": None,
                            "event_id": None,
                            "row_data": None,
                        }
                    )
                    logger.info(
                        f"Added working-only battery {battery_id} for VIN {vin}"
                    )
                else:
                    logger.info(
                        f"Skipped duplicate: Battery {battery_id} already has repair events for VIN {vin}"
                    )

    def build_battery_timelines(self):
        logger.info("Building battery lifecycle timelines...")

        # Process each battery independently
        for battery_id in self.unique_batteries:
            if (
                battery_id not in self.battery_vehicles
                or not self.battery_vehicles[battery_id]
            ):
                logger.warning(f"Battery {battery_id} has no events")
                continue

            # Create processor for this battery
            processor = BatteryProcessor(battery_id)
            self.battery_processors[battery_id] = processor

            # Sort events chronologically with deterministic tie-breaking

            events = sorted(
                self.battery_vehicles[battery_id],
                key=lambda x: (
                    x["date"],
                    0 if x["column"] == "old" else 1,  # tie-breaker
                    x.get("event_id", 0),  # stable deterministic order
                ),
            )
            # Process events through state machine
            for event in events:
                processor.process_event(event)

            # Finalize and collect intervals
            intervals = processor.finalize()
            self.battery_timelines.extend(intervals)

        logger.info(
            f"Built timelines with {len(self.battery_timelines)} intervals for {len(self.battery_processors)} batteries"
        )

    def _vehicle_was_active(
        self, vin, start_date, end_date, daily_stats_by_vehicle, vin_to_vehicle_id
    ):
        """Check if vehicle drove during the specified date range."""
        vehicle_id = vin_to_vehicle_id.get(vin)
        if vehicle_id not in daily_stats_by_vehicle:
            return False

        stats = daily_stats_by_vehicle[vehicle_id]

        # Convert date objects to pandas Timestamps for comparison
        start_ts = pd.Timestamp(start_date) if start_date else None
        end_ts = pd.Timestamp(end_date) if end_date else None

        if start_ts is None or end_ts is None:
            return False

        window = stats[(stats["date"] >= start_ts) & (stats["date"] <= end_ts)]

        if window.empty:
            return False

        valid_records = window[
            (window["km_start"] >= 0)
            & (window["km_end"] >= 0)
            & (window["km_end"] >= window["km_start"])
        ]

        if valid_records.empty:
            return False

        km_driven = (
            valid_records["km_end"]
            .subtract(valid_records["km_start"], fill_value=0)
            .sum()
        )

        return km_driven > 1

    def preprocess_erstzulassung_candidates(
        self, timelines: List[BatteryInterval]
    ) -> List[BatteryInterval]:
        """
        Pre-process erstzulassung_candidate intervals (facts, not assumptions).

        These intervals represent first-appearance events that appeared as removals,
        indicating the battery must have been installed before that date.
        We set the start date to erstzulassung of the vehicle.

        Args:
            timelines: List of BatteryInterval dicts from BatteryProcessor

        Returns:
            List of processed BatteryInterval dicts with erstzulassung starts filled
        """
        logger.info("Pre-processing erstzulassung_candidate intervals...")

        # Group by VIN for processing
        by_vin = defaultdict(list)
        for iv in timelines:
            by_vin[iv["vin"]].append(iv)

        processed_timelines = []

        for vin, intervals in by_vin.items():
            for interval in intervals:
                if interval.get("erstzulassung_candidate", False):
                    vehicle_data = self.vehicle_info.get(vin, {})
                    erstzulassung = vehicle_data.get(
                        "erstzulassung"
                    ) or vehicle_data.get("first_active_date")

                    if erstzulassung and interval["interval_end"]:
                        # Set start date to erstzulassung, but handle edge case where
                        # erstzulassung is after removal (testing scenario)
                        interval["interval_start"] = (
                            erstzulassung
                            if erstzulassung < interval["interval_end"]
                            else interval["interval_end"]
                        )
                        
                        if erstzulassung < interval["interval_end"]:
                            interval["notes"] = (
                                (interval.get("notes") or "")
                                + " | start set from erstzulassung (high-confidence scenario)"
                            )
                            interval["confidence"] = 0.9
                        else:
                            interval["notes"] = (
                                (interval.get("notes") or "")
                                + " | erstzulassung after removal date - zero duration interval"
                            )
                            interval["confidence"] = 0.1
                        logger.info(
                            f"Set erstzulassung start for battery {interval['battery_id']} in VIN {vin}: {erstzulassung}"
                        )
                    else:
                        logger.warning(
                            f"Missing erstzulassung or interval_end for erstzulassung_candidate battery {interval['battery_id']} in VIN {vin}"
                        )
                        interval["interval_start"] = interval["interval_end"]
                        interval["confidence"] = 0.0
                        interval["notes"] = (
                            interval.get("notes") or ""
                        ) + " | missing erstzulassung or interval_end"

                processed_timelines.append(interval)

        return processed_timelines

    def stitch_vehicle_timelines(
        self,
        timelines: List[BatteryInterval],
        vehicle_info: Dict[str, Any],
        daily_stats_by_vehicle: Dict[int, pd.DataFrame],
        vin_to_vehicle_id,
        conflicts,
        max_gap_days=30,
    ) -> List[BatteryInterval]:
        """
        Activity-aware timeline stitching per VIN.

        Args:
            timelines: List of BatteryInterval dicts from BatteryProcessor
            vehicle_info: Dict[vin] -> {erstzulassung, first_active_date, ...}
            daily_stats_by_vehicle: Dict[vehicle_id] -> DataFrame with daily stats
            vin_to_vehicle_id: Dict[vin] -> vehicle_id mapping
            max_gap_days: Max days to auto-fill without activity check

        Returns:
            List of stitched BatteryInterval dicts
        """

        usage_by_batt: Dict[str, List[BatteryInterval]] = defaultdict(list)
        by_vin = defaultdict(list)
        for iv in timelines:
            usage_by_batt[iv["battery_id"]].append(iv)
            by_vin[iv["vin"]].append(iv)

        def _battery_used_elsewhere(
            batt_id,
            a,
            b,
            this_vin,
        ):
            """
            Check if battery is used elsewhere during the given time period.

            Returns:
                tuple: (has_conflict: bool, conflict_object: dict or None)
                - has_conflict: True if battery is used elsewhere during [a, b)
                - conflict_object: dict with conflict details if has_conflict is True, None otherwise
                  Contains: vin, start_date, end_date
            """
            for iv in usage_by_batt.get(batt_id, []):
                if iv["vin"] == this_vin:
                    continue
                s = iv["interval_start"] or date.min
                e = iv["interval_end"] or self.today
                if s < b and a < e:
                    # Found a conflict - return immediately with conflict details
                    conflict_object = {
                        "vin": iv["vin"],
                        "start_date": iv["interval_start"],
                        "end_date": iv["interval_end"],
                        "source_event_ids": iv["source_event_ids"],
                    }
                    return True, conflict_object

            # No conflicts found
            return False, None

        stitched = []
        for vin, intervals in by_vin.items():
            if not intervals:
                continue

            # 1. Sort chronologically
            intervals.sort(
                key=lambda iv: (
                    iv["interval_start"] or iv["interval_end"] or date(1900, 1, 1),
                    iv["interval_end"] or self.today,
                )
            )

            # # ----------------
            # # 2. Middle gaps and orphaned removal fixes
            # # ----------------
            filled = []
            if len(intervals) >= 2:
                for prev, nxt in zip(intervals, intervals[1:]):
                    filled.append(prev)

                    # if nxt == intervals[-1]:
                    #     continue

                    if (
                        nxt["interval_start"] is None
                        and prev["interval_end"] is not None
                    ):

                        # Calculate gap duration, if there is no end date, assume 1 year
                        gap_end_date = nxt["interval_end"] or prev[
                            "interval_end"
                        ] + timedelta(days=365)
                        gap_days = (gap_end_date - prev["interval_end"]).days

                        # Check activity during gap
                        activity_window_end = min(
                            gap_end_date, prev["interval_end"] + timedelta(days=365)
                        )
                        was_active = self._vehicle_was_active(
                            vin,
                            prev["interval_end"],
                            activity_window_end,
                            daily_stats_by_vehicle,
                            vin_to_vehicle_id,
                        )
                        if was_active or gap_days <= max_gap_days:
                            # Vehicle was active, that means a battery must have been installed
                            has_conflict, conflict_obj = _battery_used_elsewhere(
                                nxt["battery_id"],
                                prev["interval_end"],
                                gap_end_date,
                                vin,
                            )
                            if has_conflict:
                                # Add conflict to the conflicts list
                                conflict = {
                                    "type": "gap_filling",
                                    "battery_id": nxt["battery_id"],
                                    "candidate_vin": vin,
                                    "other_vin": conflict_obj["vin"],
                                    "candidate_vin_start_day": prev["interval_end"],
                                    "candidate_vin_end_day": gap_end_date,
                                    "other_vin_start_date": conflict_obj["start_date"],
                                    "other_vin_end_date": conflict_obj["end_date"],
                                    "confidence": nxt.get("confidence", 0),
                                    "candidate_source_event_ids": nxt[
                                        "source_event_ids"
                                    ],
                                }
                                conflicts.append(conflict)
                                logger.info(conflict)

                                nxt["interval_start"] = prev["interval_end"]
                                nxt["notes"] = (
                                    (nxt.get("notes") or "")
                                    + " | start imputed from previous removal, but battery used elsewhere"
                                )
                                nxt["interval_type"] = "infer_start_record_end"
                                nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.5)
                            else:
                                nxt["interval_start"] = prev["interval_end"]
                                nxt["notes"] = (
                                    nxt.get("notes") or ""
                                ) + " | start imputed from previous removal"
                                nxt["interval_type"] = "infer_start_record_end"
                                nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.6)
                        else:
                            # Vehicle was inactive
                            nxt["interval_start"] = nxt["interval_end"]
                            nxt["notes"] = (
                                (nxt.get("notes") or "")
                                + " | zero duration - vehicle was inactive during this period"
                            )
                            nxt["confidence"] = 0.2
                            nxt["interval_type"] = "infer_start_record_end"

                    elif nxt["interval_start"] is None and prev["interval_end"] is None:
                        # Dual-battery setup: both batteries active simultaneously
                        # Keep them as concurrent intervals rather than overlapping
                        nxt["interval_start"] = prev["interval_start"]
                        nxt["notes"] = "Concurrent battery in dual-battery vehicle"
                        nxt["interval_type"] = "infer_start_record_end"
                        nxt["confidence"] = 0.6
                        prev["confidence"] = 0.6

                    # Handle small gaps between known intervals
                    elif (
                        prev["interval_end"]
                        and nxt["interval_start"]
                        and (nxt["interval_start"] - prev["interval_end"]).days > 1
                    ):
                        # Probably maintaince event
                        pass

                # Add final interval
                filled.append(intervals[-1])
            else:
                filled = intervals.copy()

            intervals = filled

            # # ----------------
            # # 3. Erstzulassung extension (vehicle can not drive without a battery)
            # # Note: erstzulassung_candidate intervals are already handled in preprocessing step
            # # ----------------
            if intervals and intervals[0]["interval_start"] is None:
                vehicle_data = vehicle_info.get(vin, {})
                start_date = vehicle_data.get("erstzulassung")

                if (
                    start_date and intervals[0]["interval_end"]
                ):  # Only if we have a real end date
                    has_conflict, conflict_obj = _battery_used_elsewhere(
                        intervals[0]["battery_id"],
                        start_date,
                        intervals[0]["interval_end"],
                        vin,
                    )
                    if not has_conflict:
                        intervals[0]["interval_start"] = start_date
                        intervals[0]["notes"] = (
                            intervals[0].get("notes") or ""
                        ) + " | start imputed from erstzulassung"
                        intervals[0]["confidence"] = 0.75
                        intervals[0]["interval_type"] = "infer_start_record_end"
                    else:
                        # Add conflict to the conflicts list
                        conflict = {
                            "type": "erstzulassung_extension",
                            "battery_id": intervals[0]["battery_id"],
                            "candidate_vin": vin,
                            "other_vin": conflict_obj["vin"],
                            "candidate_vin_start_day": start_date,
                            "candidate_vin_end_day": intervals[0]["interval_end"],
                            "other_vin_start_date": conflict_obj["start_date"],
                            "other_vin_end_date": conflict_obj["end_date"],
                            "confidence": intervals[0].get("confidence", 1.0),
                            "candidate_source_event_ids": intervals[0][
                                "source_event_ids"
                            ],
                        }
                        intervals[0]["interval_start"] = start_date
                        intervals[0]["notes"] = (
                            intervals[0].get("notes") or ""
                        ) + " | start imputed from erstzulassung"
                        intervals[0]["confidence"] = 0.3
                        intervals[0]["interval_type"] = "infer_start_record_end"
                        conflicts.append(conflict)
                        logger.info(conflict)
                else:
                    logger.warning(f"Vehicle {vin} has no erstzulassung date")
                    intervals[0]["interval_start"] = intervals[0]["interval_end"]
                    intervals[0]["confidence"] = 0
                    intervals[0]["notes"] = (
                        intervals[0].get("notes") or ""
                    ) + " | no erstzulassung or first active date"
                    intervals[0]["interval_type"] = "infer_start_record_end"

            elif intervals and intervals[0]["interval_start"] is not None:
                # Check if first interval starts after vehicle's erstzulassung
                vehicle_data = vehicle_info.get(vin, {})
                erstzulassung = vehicle_data.get("erstzulassung")

                if erstzulassung and intervals[0]["interval_start"] > erstzulassung:
                    # Gap between erstzulassung and first recorded battery
                    has_conflict, conflict_obj = _battery_used_elsewhere(
                        intervals[0]["battery_id"],
                        erstzulassung,
                        intervals[0]["interval_start"],
                        vin,
                    )
                    if not has_conflict:
                        interval = {
                            "battery_id": intervals[0]["battery_id"],
                            "vin": vin,
                            "interval_start": erstzulassung,
                            "interval_end": intervals[0]["interval_start"],
                            "interval_type": "infer_start_infer_end",
                            "source_event_ids": intervals[0]["source_event_ids"],
                            "confidence": 0.7,
                            "notes": "Imputed interval - vehicle active before first recorded battery battery not used elsewhere",
                        }
                        intervals.insert(0, interval)
                    else:
                        # Add conflict to the conflicts list
                        conflict = {
                            "type": "erstzulassung_extension",
                            "battery_id": intervals[0]["battery_id"],
                            "candidate_vin": vin,
                            "other_vin": conflict_obj["vin"],
                            "candidate_vin_start_day": erstzulassung,
                            "candidate_vin_end_day": intervals[0]["interval_start"],
                            "other_vin_start_date": conflict_obj["start_date"],
                            "other_vin_end_date": conflict_obj["end_date"],
                            "confidence": intervals[0].get("confidence", 1.0),
                            "candidate_source_event_ids": intervals[0][
                                "source_event_ids"
                            ],
                        }
                        conflicts.append(conflict)
                        logger.info(conflict)

                        interval = {
                            "battery_id": intervals[0]["battery_id"],
                            "vin": vin,
                            "interval_start": erstzulassung,
                            "interval_end": intervals[0]["interval_start"],
                            "interval_type": "infer_start_infer_end",
                            "source_event_ids": intervals[0]["source_event_ids"],
                            "confidence": 0.3,
                            "notes": "Imputed interval - vehicle active before first recorded battery, but recorded battery used elsewhere",
                        }
                        intervals.insert(0, interval)

            # ----------------
            # 3. Post-activity extension (Last interval is CLOSED, but daily-stats show km afterwards.)
            # ----------------
            last_iv = intervals[-1] if intervals else None

            if last_iv and last_iv["interval_end"] is not None:
                was_active_after = self._vehicle_was_active(
                    vin,
                    last_iv["interval_end"] + timedelta(days=90),
                    self.today,
                    daily_stats_by_vehicle,
                    vin_to_vehicle_id,
                )

                new_iv = {
                    "battery_id": last_iv["battery_id"],
                    "vin": vin,
                    "interval_start": last_iv["interval_end"],
                    "interval_end": self.today, 
                    "interval_type": "infer_start_infer_end",
                    "source_event_ids": last_iv["source_event_ids"],
                    "confidence": None,
                    "notes": "",
                }
                if was_active_after:
                    has_conflict, conflict_obj = _battery_used_elsewhere(
                        last_iv["battery_id"],
                        last_iv["interval_end"],
                        self.today,
                        vin,
                    )
                    if not has_conflict:
                        new_iv["confidence"] = 0.75
                        new_iv["notes"] = "Imputed interval - vehicle active after last removal, battery assumed to be still inside"
                    else:
                        new_iv["confidence"] = 0.3
                        new_iv["notes"] = "Imputed interval - vehicle active after last removal, battery assumed to be still inside, but battery used elsewhere"
                        conflict = {
                            "type": "post_activity_extension",
                            "battery_id": last_iv["battery_id"],
                            "candidate_vin": vin,
                            "other_vin": conflict_obj["vin"],
                            "candidate_vin_start_day": last_iv["interval_end"],
                            "candidate_vin_end_day": self.today,
                            "other_vin_start_date": conflict_obj["start_date"],
                            "other_vin_end_date": conflict_obj["end_date"],
                            "confidence": 0.3,
                            "candidate_source_event_ids": last_iv[
                                "source_event_ids"
                            ],
                            "other_vin_source_event_ids": conflict_obj[
                                "source_event_ids"
                            ],
                        }
                        conflicts.append(conflict)
                        logger.info(conflict)
                    intervals.append(new_iv)    
                    usage_by_batt[last_iv["battery_id"]].append(new_iv)
            stitched.extend(intervals)
        return stitched, conflicts

    def resolve_battery_conflicts(
        self, stitched_timelines: List[BatteryInterval], conflicts, vehicle_info
    ):
        if not conflicts:
            logger.info("No conflicts to resolve")
            return stitched_timelines

        # Build sacred intervals index - these are NEVER modified during conflict resolution
        sacred_battery_erstzulassung_intervals = defaultdict()
        for iv in stitched_timelines:
            if iv.get("erstzulassung_candidate", False):
                sacred_battery_erstzulassung_intervals[iv["battery_id"]] = {
                    "vin": iv["vin"],
                    "interval_start": iv["interval_start"],
                    "interval_end": iv["interval_end"],
                }

        # Build O(1) lookup index: (battery_id, vin) -> list of intervals
        timeline_index = defaultdict(list)
        for iv in stitched_timelines:
            key = (iv["battery_id"], iv["vin"])
            timeline_index[key].append(iv)

        # Group conflicts by battery and type
        erstzulassung_conflicts_by_batt = defaultdict(list)
        post_activity_conflicts_by_batt = defaultdict(list)
        for conflict in conflicts:
            if conflict["type"] == "erstzulassung_extension":
                bat = conflict["battery_id"]
                erstzulassung_conflicts_by_batt[bat].append(conflict)
            elif conflict["type"] == "post_activity_extension":
                bat = conflict["battery_id"]
                post_activity_conflicts_by_batt[bat].append(conflict)

        # Resolve erstzulassung conflicts while respecting sacred intervals
        for bat, conflicts in erstzulassung_conflicts_by_batt.items():
            logger.info(f"Resolving erstzulassung conflicts for battery {bat}")

            # Sort conflicts by earliest start date and source event IDs
            conflicts.sort(
                key=lambda x: (
                    x["candidate_vin_start_day"],
                    (
                        min(x["candidate_source_event_ids"])
                        if x["candidate_source_event_ids"]
                        else float("inf")
                    ),
                    x["candidate_vin_end_day"],
                )
            )

            # Use sweep line algorithm to sequence intervals
            intervals_to_resolve = []
            for conflict in conflicts:
                lookup_key = (bat, conflict["candidate_vin"])
                if lookup_key in timeline_index:
                    for iv in timeline_index[lookup_key]:
                        if (
                            iv["interval_start"] is None
                            and iv["interval_end"] == conflict["candidate_vin_end_day"]
                        ):
                            intervals_to_resolve.append(
                                {
                                    "interval": iv,
                                    "conflict": conflict,
                                    "desired_start": conflict[
                                        "candidate_vin_start_day"
                                    ],
                                }
                            )
                            break

            if not intervals_to_resolve:
                continue

            # Sort intervals by desired start date
            intervals_to_resolve.sort(key=lambda x: x["desired_start"])

            # Sequential assignment with sweep line logic

            if bat in sacred_battery_erstzulassung_intervals.keys():
                current_start_date = sacred_battery_erstzulassung_intervals[bat][
                    "interval_end"
                ]
            else:
                current_start_date = intervals_to_resolve[0]["desired_start"]

            for i, item in enumerate(intervals_to_resolve):
                iv = item["interval"]
                conflict = item["conflict"]

                # Ensure start <= end
                if current_start_date <= iv["interval_end"]:
                    iv["interval_start"] = current_start_date
                    iv["notes"] = (
                        (iv["notes"] or "")
                        + f" | start imputed from sequential conflict resolution with {conflict['other_vin']} (position {i})"
                    )
                    iv["confidence"] = max(
                        0.6 - (i * 0.1), 0.3
                    )  # Decrease confidence for later assignments
                    iv["interval_type"] = "infer_start_record_end"

                    logger.info(
                        f"  → RESOLVED: Set {conflict['candidate_vin']} start to {current_start_date} (position {i})"
                    )

                    # Next interval starts when this one ends
                    current_start_date = iv["interval_end"]
                else:
                    # start > end -> set start to end (zero duration)
                    iv["interval_start"] = iv["interval_end"]
                    iv["notes"] = (
                        (iv["notes"] or "")
                        + f" | start set to end due to conflict resolution with {conflict['other_vin']} (position {i})"
                    )
                    iv["confidence"] = 0.2
                    iv["interval_type"] = "infer_start_record_end"

                    logger.info(
                        f"  → RESOLVED (zero duration): Set {conflict['candidate_vin']} start=end={iv['interval_end']} (position {i})"
                    )

        for bat, conflicts in post_activity_conflicts_by_batt.items():
            logger.info(f"Resolving post-activity conflicts for battery {bat}")
        return stitched_timelines

    def limit_concurrent_batteries_per_vin(
        self, stitched_timelines: List[BatteryInterval]
    ) -> List[BatteryInterval]:
        """
        Ensure each VIN has at most 2 active batteries at any point in time.
        Uses sweep line algorithm to detect and resolve historical overlaps.
        """
        logger.info(
            "Limiting concurrent batteries per VIN to maximum 2 (historical + current)..."
        )

        # Group by VIN
        by_vin = defaultdict(list)
        for iv in stitched_timelines:
            by_vin[iv["vin"]].append(iv)

        for vin, intervals in by_vin.items():
            if len(intervals) <= 2:
                continue  # No possible overlaps

            # Build events list: (date, event_type, interval_obj)
            events = []
            for iv in intervals:
                start_date = iv["interval_start"] or date.min
                end_date = iv["interval_end"] or self.today

                events.append((start_date, "start", iv))
                if (
                    iv["interval_end"] is not None
                ):  # Don't add end event for open intervals
                    events.append((end_date, "end", iv))

            # Sort by date, with "end" events before "start" events on same date
            events.sort(key=lambda x: (x[0], x[1] == "start"))

            # Sweep line algorithm
            active_intervals = []

            for event_date, event_type, interval_obj in events:
                if event_type == "start":
                    # Validate interval has positive duration before considering it active
                    start_date = interval_obj["interval_start"]
                    end_date = interval_obj["interval_end"]

                    # Only add to active if it has positive duration
                    if start_date and end_date and start_date < end_date:
                        active_intervals.append(interval_obj)
                    elif start_date and end_date == self.today:
                        # Open interval (currently active) - also valid
                        active_intervals.append(interval_obj)
                    else:
                        # Zero duration or invalid interval - skip
                        logger.debug(
                            f"Skipping invalid interval for battery {interval_obj['battery_id']}: start={start_date}, end={end_date}"
                        )
                        continue

                    # Check if we exceed 2 concurrent batteries
                    if len(active_intervals) > 2:
                        logger.warning(
                            f"VIN {vin} has {len(active_intervals)} concurrent batteries on {event_date}"
                        )

                        # Sort by confidence (lowest first) and close the worst one
                        active_intervals.sort(
                            key=lambda x: (
                                x.get("confidence", 0),
                                max(
                                    x.get("source_event_ids", [0]) or [0]
                                ),  # Lower max IDs come first
                            )
                        )

                        # Close the lowest confidence interval
                        interval_to_close = active_intervals.pop(0)
                        interval_to_close["interval_end"] = event_date
                        interval_to_close["notes"] = (
                            interval_to_close.get("notes", "")
                            + f" | Closed on {event_date} due to >2 concurrent batteries limit"
                        )

                        logger.info(
                            f"  → Closed battery {interval_to_close['battery_id']} on {event_date}"
                        )
                        logger.info(" ")

                elif event_type == "end":
                    # Remove from active set
                    active_intervals = [
                        iv for iv in active_intervals if iv != interval_obj
                    ]

        return stitched_timelines

    def generate_outputs(self):
        """Generate output files."""
        logger.info("Generating output files...")

        # Generate enhanced timeline CSV output
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            timeline_df = pd.DataFrame(self.battery_timelines)
            timeline_csv = f"battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        # Generate statistics file
        stats_filename = f"battery_timeline_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Timeline Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {len(self.unique_batteries)}\n")
            f.write(f"Total Vehicles: {len(self.unique_vehicles)}\n")
            f.write(f"Working-only Vehicles: {self.stats['working_only_vehicles']}\n")
            f.write(f"Errors: {len(self.stats['errors'])}\n")

            # Enhanced statistics
            if hasattr(self, "battery_timelines"):
                f.write(f"Total Timeline Intervals: {len(self.battery_timelines)}\n")
                active_intervals = len(
                    [i for i in self.battery_timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(self.battery_timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

            if self.stats["errors"]:
                f.write("\nErrors:\n")
                for error in self.stats["errors"]:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        # Return the timeline CSV if it exists, otherwise return None
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            return timeline_csv, stats_filename
        else:
            return None, stats_filename

    def generate_timeline_outputs(self):
        """Generate timeline output files only."""
        logger.info("Generating timeline output files...")

        # Generate timeline CSV output
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            timeline_df = pd.DataFrame(self.battery_timelines)
            timeline_csv = f"battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

            # Generate timeline statistics
            stats_filename = f"battery_timeline_statistics.txt"
            with open(stats_filename, "w") as f:
                f.write("Battery Timeline Analysis Statistics\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Processing Date: {datetime.now()}\n")
                f.write(f"Total Batteries: {len(self.battery_processors)}\n")
                f.write(f"Total Timeline Intervals: {len(self.battery_timelines)}\n")

                active_intervals = len(
                    [i for i in self.battery_timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(self.battery_timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

                # Confidence distribution
                confidences = [i["confidence"] for i in self.battery_timelines]
                avg_confidence = (
                    sum(confidences) / len(confidences) if confidences else 0
                )
                f.write(f"Average Confidence: {avg_confidence:.3f}\n")

                # Interval type distribution
                interval_types = {}
                for interval in self.battery_timelines:
                    interval_type = interval["interval_type"]
                    interval_types[interval_type] = (
                        interval_types.get(interval_type, 0) + 1
                    )

                f.write(f"\nInterval Type Distribution:\n")
                for interval_type, count in interval_types.items():
                    f.write(f"  {interval_type}: {count}\n")

            logger.info(f"Saved timeline statistics to {stats_filename}")
            return timeline_csv
        else:
            logger.warning("No timeline data to output")
            return None

    def validate_timelines(self):
        logger.info("Validating timelines...")

        def _validate_erstzulassung_correctness(battery_timelines):
            """
            Validate that vehicles with erstzulassung_candidate intervals
            actually have their first timeline interval starting with the expected VIN.
            """
            logger.info("Validating erstzulassung correctness...")

            # Only process timelines with valid start and end dates
            battery_timelines = [
                iv
                for iv in battery_timelines
                if iv["interval_start"] is not None and iv["interval_end"] is not None and iv["interval_start"] < iv["interval_end"]
            ]

            # Step 1: Find all VINs that have erstzulassung_candidate intervals
            erstzulassung_candidate_vins = set()
            erstzulassung_intervals = []
            for interval in battery_timelines:
                if interval.get("erstzulassung_candidate", False):
                    erstzulassung_candidate_vins.add(interval["vin"])
                    erstzulassung_intervals.append(interval)

            logger.info(
                f"Found {len(erstzulassung_candidate_vins)} VINs with erstzulassung_candidate intervals"
            )

            # Step 2: Group timelines by VIN and sort chronologically
            timelines_by_vin = defaultdict(list)
            for interval in battery_timelines:
                if interval["interval_start"] is None or interval["interval_end"] is None:
                    continue
                timelines_by_vin[interval["vin"]].append(interval)

            # Sort each VIN's intervals chronologically
            for vin in timelines_by_vin:
                timelines_by_vin[vin].sort(
                    key=lambda iv: (
                        iv["interval_start"] or iv["interval_end"] or date(1900, 1, 1),
                        iv["interval_end"] or self.today,
                    )
                )

            # Step 3: Build battery usage index for conflict detection
            battery_usage = defaultdict(list)
            for interval in battery_timelines:
                battery_usage[interval["battery_id"]].append(interval)

            # Step 4: Validate each erstzulassung_candidate interval
            validation_errors = []
            validation_successes = []

            for interval in erstzulassung_intervals:
                vin = interval["vin"]
                battery_id = interval["battery_id"]
                start_date = interval["interval_start"]
                end_date = interval["interval_end"]

                # Check if this is the first interval for this VIN
                if vin not in timelines_by_vin or not timelines_by_vin[vin]:
                    validation_errors.append(
                        f"VIN {vin} has erstzulassung_candidate but no timeline intervals"
                    )
                    continue

                first_interval = timelines_by_vin[vin][0]

                # Check if the first interval is indeed for this VIN
                if first_interval["vin"] != vin:
                    validation_errors.append(
                        f"VIN {vin} erstzulassung validation failed: "
                        f"first interval belongs to VIN {first_interval['vin']}, not {vin}"
                    )
                    continue

                if start_date is None:
                    validation_errors.append(
                        f"VIN {vin} erstzulassung validation failed: "
                        f"first interval has no start date despite erstzulassung_candidate processing"
                    )
                    continue

                # NEW: Check if battery is used elsewhere during erstzulassung period
                battery_conflicts = []
                for other_interval in battery_usage[battery_id]:
                    if other_interval["vin"] == vin:
                        continue  # Skip same VIN

                    other_start = other_interval["interval_start"]
                    other_end = other_interval["interval_end"] or self.today
                    period_end = end_date or date.max
                    if other_start is None:
                        continue

                    if start_date < other_end and other_start < period_end:
                        battery_conflicts.append(other_interval["vin"])

                if battery_conflicts:
                    validation_errors.append(
                        f"VIN {vin} erstzulassung validation failed: "
                        f"battery {battery_id} also used in VINs {battery_conflicts} during erstzulassung period"
                    )
                else:
                    validation_successes.append(
                        f"VIN {vin} erstzulassung validation passed: "
                        f"battery {battery_id} exclusively used, starts on {start_date}"
                    )

            # Step 5: Report validation results
            logger.info(f"Erstzulassung validation completed:")
            logger.info(f"  - {len(validation_successes)} VINs passed validation")
            logger.info(f"  - {len(validation_errors)} VINs failed validation")

            if validation_errors:
                logger.warning("Validation errors found:")
                for error in validation_errors:
                    logger.warning(f"  ✗ {error}")

            return len(validation_errors) == 0

        def _validate_battery_exclusivity(timelines: List[dict]) -> bool:
            """
            Validate that each battery is only used in one vehicle at any given time.
            Check for overlapping intervals for the same battery across different VINs.
            """
            logger.info("Validating battery exclusivity...")

            # Group intervals by battery_id
            battery_usage = defaultdict(list)
            for interval in timelines:
                start = interval.get("interval_start")
                end = interval.get("interval_end")
                if start is None or end is None or start >= end:
                    continue
                battery_usage[interval["battery_id"]].append(interval)

            exclusivity_violations = []
            total_batteries_checked = 0

            for battery_id, intervals in battery_usage.items():
                total_batteries_checked += 1
                if len(intervals) <= 1:
                    continue  # Single interval, no conflicts possible

                # Create events for sweep line
                events = []
                for interval in intervals:
                    events.append((interval["interval_start"], 'start', interval))
                    events.append((interval["interval_end"], 'end', interval))

                # Sort events: by time ascending, then process 'end' before 'start' for same time
                events.sort(key=lambda e: (e[0], 0 if e[1] == 'end' else 1))

                # Active intervals grouped by VIN: vin -> list of active intervals
                active_vins = defaultdict(list)

                battery_violations = []

                for time, event_type, interval in events:
                    vin = interval["vin"]
                    if event_type == 'start':
                        # Check for overlaps with active intervals from other VINs
                        for other_vin, active_intervals in active_vins.items():
                            if other_vin == vin:
                                continue
                            for active_interval in active_intervals:
                                # Prepare violation with sorted VINs for consistency
                                vin1, vin2 = sorted([vin, other_vin])
                                if vin1 == interval["vin"]:
                                    start1, end1 = interval["interval_start"], interval["interval_end"]
                                    start2, end2 = active_interval["interval_start"], active_interval["interval_end"]
                                else:
                                    start1, end1 = active_interval["interval_start"], active_interval["interval_end"]
                                    start2, end2 = interval["interval_start"], interval["interval_end"]
                                battery_violations.append(
                                    {
                                        "battery_id": battery_id,
                                        "vin1": vin1,
                                        "vin1_start": start1,
                                        "vin1_end": end1,
                                        "vin2": vin2,
                                        "vin2_start": start2,
                                        "vin2_end": end2,
                                    }
                                )
                        # Add this interval to active
                        active_vins[vin].append(interval)
                    elif event_type == 'end':
                        # Remove this interval from active
                        if interval in active_vins[vin]:
                            active_vins[vin].remove(interval)

                # Add to total violations
                exclusivity_violations.extend(battery_violations)

            # Report results
            logger.info(f"Battery exclusivity validation completed:")
            logger.info(f"  - {total_batteries_checked} batteries checked")
            logger.info(
                f"  - {len(exclusivity_violations)} exclusivity violations found"
            )

            if exclusivity_violations:
                logger.warning("Battery exclusivity violations found:")
                for violation in exclusivity_violations[:10]:  # Show first 10
                    logger.warning(
                        f"  ✗ Battery {violation['battery_id']}: "
                        f"VIN {violation['vin1']} ({violation['vin1_start']} to {violation['vin1_end']}) "
                        f"overlaps with VIN {violation['vin2']} ({violation['vin2_start']} to {violation['vin2_end']})"
                    )
                if len(exclusivity_violations) > 10:
                    logger.warning(
                        f"  ... and {len(exclusivity_violations) - 10} more violations"
                    )

            return len(exclusivity_violations) == 0

        _validate_battery_exclusivity(self.battery_timelines)
        _validate_erstzulassung_correctness(self.battery_timelines)

    def resolve_battery_timelines(self, battery_timelines):
        by_battery = defaultdict(list)
        for iv in battery_timelines:
            by_battery[iv['battery_id']].append(iv)
        new_timelines = []
        for ivs in by_battery.values():
            resolved = self._resolve_overlaps_for_battery(ivs)
            new_timelines.extend(resolved)
        return new_timelines 

    def _resolve_overlaps_for_battery(self, intervals: List[BatteryInterval]) -> List[BatteryInterval]:
        if len(intervals) <= 1:
            return intervals

        # Filter valid and invalid intervals
        invalids = []
        valid_intervals = []
        for iv in intervals:
            start = iv.get('interval_start')
            end = iv.get('interval_end')
            if start is None or end is None or start >= end:
                iv['notes'] += " | Invalid interval, skipped resolution"
                invalids.append(iv)
            else:
                valid_intervals.append(iv)

        if not valid_intervals:
            return intervals  # All invalid, return as modified

        events = []
        for iv in valid_intervals:
            events.append((iv['interval_start'], 'start', iv))
            events.append((iv['interval_end'], 'end', iv))

        # Sort events: by time ascending, process 'end' before 'start' for same time
        events.sort(key=lambda e: (e[0], 0 if e[1] == 'end' else 1))

        active = []
        last_time = None
        assigned_segments = defaultdict(list)  # id(iv) -> list of (start, end) segments

        for time, event_type, iv in events:
            if last_time is not None and time > last_time and active:
                # Assign segment to highest-priority active interval
                def priority_key(x):
                    max_source_id = max([sid for sid in x['source_event_ids'] if sid is not None] or [0])
                    return (x['confidence'], max_source_id, x['vin'])

                max_iv = max(active, key=priority_key)
                assigned_segments[id(max_iv)].append((last_time, time))

            if event_type == 'start':
                active.append(iv)
            elif event_type == 'end':
                if iv in active:
                    active.remove(iv)

            last_time = time

        # Process assigned segments for each original valid interval
        new_intervals = [] 
        for iv in valid_intervals:
            # Each interval owns a vin, and after sweep line, it may contains no or many segments
            segs = assigned_segments.get(id(iv), [])
            if not segs:
                iv['notes'] += " | Trimmed to empty in overlap resolution"
                iv["interval_start"] = iv["interval_end"]
                invalids.append(iv)
                continue 

            # Merge adjacent segments
            segs.sort(key=lambda s: s[0])
            merged = []
            for s in segs:
                if not merged or merged[-1][1] < s[0]:
                    merged.append(list(s))
                else:
                    # adjacent
                    merged[-1][1] = max(merged[-1][1], s[1])

            # Handle merged segments (split if disjoint)
            for j, m in enumerate(merged):
                if j > 0:
                    # Create new interval for split
                    new_iv = iv.copy()
                    new_iv['interval_start'] = m[0]
                    new_iv['interval_end'] = m[1]
                    new_iv['notes'] += f" | Split in overlap resolution ({j+1}/{len(merged)})"
                    new_intervals.append(new_iv)
                else:
                    # Update original
                    iv['interval_start'] = m[0]
                    iv['interval_end'] = m[1]
                    iv['notes'] += " | Trimmed in overlap resolution"
                    new_intervals.append(iv)

        # Include invalids in the output
        new_intervals.extend(invalids)

        return new_intervals
    
    def post_cleanup_timelines(self, timelines: List[BatteryInterval]) -> List[BatteryInterval]:
        """
        Clean up timeline intervals by removing invalid ones.
        
        Removes intervals that have:
        1. Missing start or end dates
        2. Start date >= end date
        
        Args:
            timelines: List of BatteryInterval dicts
        
        Returns:
            List of cleaned BatteryInterval dicts
        """
        logger.info("Starting post-cleanup timeline validation...")
        
        initial_count = len(timelines)
        cleaned_timelines = []
        
        removed_empty_dates = 0
        removed_invalid_duration = 0
        
        for interval in timelines:
            start = interval.get("interval_start")
            end = interval.get("interval_end")
            
            # Check for missing start or end dates
            if start is None or end is None:
                removed_empty_dates += 1
                logger.debug(
                    f"Removed interval with empty dates - Battery {interval['battery_id']}, "
                    f"VIN {interval['vin']}, start={start}, end={end}"
                )
                continue
                
            # Check for invalid duration (start >= end)
            if start >= end:
                removed_invalid_duration += 1
                logger.debug(
                    f"Removed interval with invalid duration - Battery {interval['battery_id']}, "
                    f"VIN {interval['vin']}, start={start}, end={end}"
                )
                continue
                
            # Valid interval
            cleaned_timelines.append(interval)
        
        final_count = len(cleaned_timelines)
        total_removed = initial_count - final_count
        
        if total_removed > 0:
            logger.info(f"Post-cleanup removed {total_removed} invalid intervals:")
            logger.info(f"  - {removed_empty_dates} intervals with missing start/end dates")
            logger.info(f"  - {removed_invalid_duration} intervals with start >= end")
            logger.info(f"Remaining valid intervals: {final_count}")
        else:
            logger.info("Post-cleanup: All intervals are valid")
        
        return cleaned_timelines
    
    def run(self):
        """Run the battery timeline analysis process."""
        logger.info("Starting battery timeline analysis...")

        try:
            self.load_data()
            self.clean_data()
            self.process_hv_repair_data()
            self.build_vehicle_info()
            self.add_vehicles_from_working_only_data()

            # Set basic stats for timeline analysis
            self.stats["total_batteries"] = len(self.unique_batteries)
            self.stats["total_vehicles"] = len(self.unique_vehicles)

            logger.info("Running timeline analysis...")
            self.build_battery_timelines()

            logger.info("Battery Timeline completed")
            self.raw_battery_timelines = self.battery_timelines.copy()

            self.battery_timelines = self.preprocess_erstzulassung_candidates(
                self.battery_timelines
            )

            erstzulassung_timeline = self.battery_timelines.copy()
            erstzulassung_timeline_df = pd.DataFrame(erstzulassung_timeline)
            erstzulassung_timeline_csv = "battery_lifecycle_timelines_erstzulassung.csv"
            erstzulassung_timeline_df.to_csv(erstzulassung_timeline_csv, index=False)
            logger.info(
                f"Saved erstzulassung timeline data to {erstzulassung_timeline_csv}"
            )

            if self.raw_battery_timelines:
                raw_timeline_df = pd.DataFrame(self.raw_battery_timelines)
                raw_timeline_csv = "battery_lifecycle_timelines_raw.csv"
                raw_timeline_df.to_csv(raw_timeline_csv, index=False)
                logger.info(f"Saved raw timeline data to {raw_timeline_csv}")

            # logger.info("Validating timelines before stitching...")
            # self.validate_timelines(self.battery_timelines)

            self.battery_timelines, self.conflicts = self.stitch_vehicle_timelines(
                self.battery_timelines,
                self.vehicle_info,
                self.daily_stats_by_vehicle,
                self.vin_to_vehicle_id,
                self.conflicts,
            )
            post_activity_timeline = self.battery_timelines.copy()
            post_activity_timeline_df = pd.DataFrame(post_activity_timeline)
            post_activity_timeline_csv = "battery_lifecycle_timelines_post_activity.csv"
            post_activity_timeline_df.to_csv(post_activity_timeline_csv, index=False)

            self.battery_timelines = self.resolve_battery_timelines(self.battery_timelines)
            self.battery_timelines = self.limit_concurrent_batteries_per_vin(
                self.battery_timelines
            )
            self.battery_timelines = self.post_cleanup_timelines(self.battery_timelines)
            self.validate_timelines()
            # self.battery_timelines = self.resolve_battery_conflicts(
            #     self.battery_timelines,
            #     self.conflicts,
            #     self.vehicle_info,
            # )

            # self.validate_timelines(self.battery_timelines)
            csv_file, stats_file = self.generate_outputs()

            logger.info("Battery timeline analysis completed successfully!")
            if csv_file:
                logger.info(f"Timeline data saved to: {csv_file}")
            logger.info(f"Statistics saved to: {stats_file}")

            logger.info("Enhanced timeline analysis completed!")
            logger.info(f"Timeline intervals: {len(self.battery_timelines)}")
            logger.info(f"Batteries with timelines: {len(self.battery_processors)}")

            # Generate dual-battery report
            dual_battery_report = self.generate_dual_battery_report()
            logger.info(
                f"Dual-battery vehicles found: {dual_battery_report['total_dual_battery_vehicles']}"
            )

            # Optionally save to stats file
            with open(stats_file, "a") as f:
                f.write(f"\nDual-Battery Configuration:\n")
                f.write(
                    f"  Vehicles with 2 active batteries: {dual_battery_report['total_dual_battery_vehicles']}\n"
                )
                f.write(
                    f"  VINs: {', '.join(dual_battery_report['dual_battery_vins'][:10])}{'...' if len(dual_battery_report['dual_battery_vins']) > 10 else ''}\n"
                )

            return csv_file, stats_file

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise

    def generate_dual_battery_report(self):
        """Generate report on dual-battery configurations"""
        dual_battery_vins = []

        # Group intervals by VIN
        by_vin = defaultdict(list)
        for iv in self.battery_timelines:
            by_vin[iv["vin"]].append(iv)

        for vin, intervals in by_vin.items():
            # Count currently active batteries (interval_end is None)
            concurrent = len([iv for iv in intervals if iv["interval_end"] is None])
            if concurrent == 2:
                dual_battery_vins.append(vin)

        return {
            "total_dual_battery_vehicles": len(dual_battery_vins),
            "dual_battery_vins": dual_battery_vins,
        }


class DataPreparator:
    """
    Handles loading, cleaning, and preparing data (including database connection, VIN mappings, processing repair data, building vehicle info, adding working-only batteries).
    """
    def __init__(self):
        self.today = datetime.now().date()
        self.db_engine = None
        self.hv_repair_df = None
        self.working_vehicles_df = None
        self.working_unique_df = None
        self.daily_stats_df = None
        self.daily_stats_by_vehicle = {}
        self.vin_to_vehicle_id = {}
        self.battery_vehicles = {}
        self.vehicle_info = {}
        self.unique_batteries = set()
        self.unique_vehicles = set()
        self.vin_without_vehicle_id = set()
        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def _initialize_database_connection(self):
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")
        db_connection_string = (
            f"postgresql://{user}:{password}@{host}:{port}/{database}"
        )
        try:
            self.db_engine = create_engine(db_connection_string)
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            logger.error(
                "Pipeline requires PostgreSQL connection for activity validation"
            )
            logger.error(f"Connection string: {db_connection_string}")
            logger.error("Please ensure PostgreSQL server is running and accessible")
            raise ConnectionError(f"Required database connection failed: {e}")

    def _load_vin_mappings(self):
        if not self.db_engine:
            logger.warning(
                "No database connection - cant not load VIN to vehicle_id mapping"
            )
            raise
        try:
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles 
            WHERE vin IS NOT NULL
            """
            mapping_df = pd.read_sql(mapping_query, self.db_engine)
            logger.info(
                f"Loaded VIN mapping for {len(mapping_df):,} vehicles from database"
            )
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]
            logger.info(
                f"Built VIN to vehicle_id mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )
        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    def load_data(self):
        logger.info("Loading data files...")
        self._initialize_database_connection()
        logger.info("Loading HV repair data...")
        self.hv_repair_df = pd.read_csv("hv_repair_2025-06-02b.csv")
        logger.info(f"Loaded {len(self.hv_repair_df)} HV repair records")
        logger.info("Loading working vehicles data and daily stats...")
        self.working_vehicles_df = pd.read_csv(
            "comparison_results/working_matching_vehicles.csv"
        )
        self.working_unique_df = pd.read_csv(
            "comparison_results/working_unique_vehicles.csv"
        )
        self.daily_stats_df = pd.read_csv(
            "daily_stats.csv",
            dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
            parse_dates=["date"],
        )
        logger.info(f"Loaded {len(self.working_vehicles_df)} matching vehicles")
        logger.info(f"Loaded {len(self.working_unique_df)} unique vehicles")
        logger.info(f"Loaded {len(self.daily_stats_df)} daily stats records")
        self.working_vehicles_df = pd.concat(
            [self.working_vehicles_df, self.working_unique_df], ignore_index=True
        )
        logger.info(f"Total working vehicles: {len(self.working_vehicles_df)}")
        logger.info("Loading VIN to vehicle_id mapping from database...")
        self._load_vin_mappings()
        logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")
        for vehicle_id, group in self.daily_stats_df.groupby("vehicle_id"):
            vehicle_data = group.sort_values("date").copy()
            self.daily_stats_by_vehicle[vehicle_id] = vehicle_data
        logger.info(
            f"Pre-indexed daily stats for {len(self.daily_stats_by_vehicle):,} vehicles"
        )
        logger.info(
            "Memory optimization: clear dailystats dataframe as we now have indexed data"
        )
        self.daily_stats_df = None

    def clean_data(self):
        logger.info("Cleaning data...")
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                ["nan", "", " ", "None"], None
            )
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )
        for col in ["master", "slave"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(
                    str
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )
        self.hv_repair_df = self.hv_repair_df.dropna(subset=["vin", "effective_date"])
        self.working_vehicles_df = self.working_vehicles_df.dropna(subset=["vin"])
        logger.info(
            f"After cleaning: {len(self.hv_repair_df)} repair records, {len(self.working_vehicles_df)} working vehicles"
        )

    def process_hv_repair_data(self):
        logger.info("Processing HV repair data...")
        self.hv_repair_df = self.hv_repair_df.sort_values("effective_date").reset_index(
            drop=True
        )
        for idx, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            effective_date = row["effective_date"].date()
            old_battery = row["battery_id_old"]
            new_battery = row["battery_id_new"]
            event_id = idx
            if old_battery and pd.notna(old_battery):
                self.unique_batteries.add(old_battery)
                if old_battery not in self.battery_vehicles:
                    self.battery_vehicles[old_battery] = []
                self.battery_vehicles[old_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "old",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {old_battery} appeared as old in vehicle {vin} on {effective_date}"
                )
            if new_battery and pd.notna(new_battery):
                self.unique_batteries.add(new_battery)
                if new_battery not in self.battery_vehicles:
                    self.battery_vehicles[new_battery] = []
                self.battery_vehicles[new_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "new",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {new_battery} appeared as new in vehicle {vin} on {effective_date}"
                )
            self.unique_vehicles.add(vin)
        logger.info(f"Found {len(self.unique_batteries)} batteries from repair data")

    def _get_first_active_date_for_vin(self, vin: str):
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None
        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get first active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None
            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]
            if len(vehicle_data) > 0:
                first_date = vehicle_data["date"].min()
                if pd.notna(first_date):
                    return first_date.date()
            return None
        except Exception as e:
            logger.error(f"Error getting first active date for {vin}: {e}")
            return None

    def _get_last_active_date_for_vin(self, vin: str):
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None
        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get last active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None
            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]
            if len(vehicle_data) > 0:
                last_date = vehicle_data["date"].max()
                if pd.notna(last_date):
                    return last_date.date()
            return None
        except Exception as e:
            logger.error(f"Error getting last active date for {vin}: {e}")
            return None

    def build_vehicle_info(self):
        logger.info("Building vehicle information...")
        for _, row in self.working_vehicles_df.iterrows():
            vin = row["vin"]
            self.unique_vehicles.add(vin)
            first_active_date = self._get_first_active_date_for_vin(vin)
            self.vehicle_info[vin] = {
                "vin": vin,
                "erstzulassung": (
                    row.get("erstzulassung").date()
                    if pd.notna(row.get("erstzulassung"))
                    else first_active_date
                ),
                "first_active_date": first_active_date,
                "master_battery": row.get("master"),
                "slave_battery": row.get("slave"),
                "akz": row.get("akz"),
                "first_active_date": first_active_date,
                "last_active_date": self._get_last_active_date_for_vin(vin),
            }
        for _, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            if vin not in self.vehicle_info:
                first_active_date = self._get_first_active_date_for_vin(vin)
                self.vehicle_info[vin] = {
                    "vin": vin,
                    "erstzulassung": first_active_date,
                    "first_active_date": first_active_date,
                    "master_battery": None,
                    "slave_battery": None,
                    "akz": None,
                    "last_active_date": self._get_last_active_date_for_vin(vin),
                }
        logger.info(f"Built info for {len(self.vehicle_info)} vehicles")
        vehicles_with_both_batteries = 0
        for vehicle_info in self.vehicle_info.values():
            if vehicle_info["master_battery"] and vehicle_info["slave_battery"]:
                vehicles_with_both_batteries += 1
        logger.info(
            f"Dual-battery vehicles from working data: {vehicles_with_both_batteries}"
        )

    def add_vehicles_from_working_only_data(self):
        logger.info("Adding batteries from working-only vehicles...")
        repair_vins = set(self.hv_repair_df["vin"].unique())
        working_vins = set(self.vehicle_info.keys())
        working_only_vins = working_vins - repair_vins
        for vin in working_only_vins:
            self._add_working_only_batteries(vin)
        self.stats["working_only_vehicles"] = len(working_only_vins)
        logger.info(
            f"Added batteries from {len(working_only_vins)} working-only vehicles"
        )

    def _add_working_only_batteries(self, vin: str):
        vehicle_info = self.vehicle_info[vin]
        erstzulassung = vehicle_info["erstzulassung"]
        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return
        for battery_field in ["master_battery", "slave_battery"]:
            battery_id = vehicle_info[battery_field]
            if battery_id and pd.notna(battery_id):
                self.unique_batteries.add(battery_id)
                if battery_id not in self.battery_vehicles:
                    self.battery_vehicles[battery_id] = []
                if not any(e["vin"] == vin for e in self.battery_vehicles[battery_id]):
                    self.battery_vehicles[battery_id].append(
                        {
                            "vin": vin,
                            "date": erstzulassung,
                            "column": None,
                            "event_id": None,
                            "row_data": None,
                        }
                    )
                    logger.info(
                        f"Added working-only battery {battery_id} for VIN {vin}"
                    )
                else:
                    logger.info(
                        f"Skipped duplicate: Battery {battery_id} already has repair events for VIN {vin}"
                    )

    def load_and_prepare_data(self):
        self.load_data()
        self.clean_data()
        self.process_hv_repair_data()
        self.build_vehicle_info()
        self.add_vehicles_from_working_only_data()
        self.stats["total_batteries"] = len(self.unique_batteries)
        self.stats["total_vehicles"] = len(self.unique_vehicles)
        return {
            'hv_repair_df': self.hv_repair_df,
            'working_vehicles_df': self.working_vehicles_df,
            'daily_stats_by_vehicle': self.daily_stats_by_vehicle,
            'vin_to_vehicle_id': self.vin_to_vehicle_id,
            'battery_vehicles': self.battery_vehicles,
            'vehicle_info': self.vehicle_info,
            'unique_batteries': self.unique_batteries,
            'unique_vehicles': self.unique_vehicles,
            'vin_without_vehicle_id': self.vin_without_vehicle_id,
            'stats': self.stats,
        }


if __name__ == "__main__":
    import sys

    # Run timeline analysis only (age calculations are commented out)
    logger.info("Running battery timeline analysis...")
    timeline_generator = BatteryTimelineGenerator()
    timeline_generator.run()
